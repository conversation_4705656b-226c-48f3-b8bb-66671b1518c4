// src/controllers/Tools/Business/businessQAPrompts.js

/**
 * Constructs an advanced, detailed prompt for generating business Q&A responses,
 * with structured tags for rich component-based rendering on the frontend.
 * @param {object} formData - The validated data from the frontend form.
 * @returns {string} The complete prompt to be sent to the Gemini AI.
 */
export const buildBusinessQAPrompt = (formData) => {
    const {
        question,
        businessContext,
        industry,
        businessStage,
        specificArea,
        language = 'English'
    } = formData;

    // Build context information
    const contextInfo = [];
    if (businessContext && businessContext.trim()) {
        contextInfo.push(`Business Context: ${businessContext}`);
    }
    if (industry && industry.trim()) {
        contextInfo.push(`Industry: ${industry}`);
    }
    if (businessStage && businessStage.trim()) {
        contextInfo.push(`Business Stage: ${businessStage}`);
    }
    if (specificArea && specificArea.trim()) {
        contextInfo.push(`Focus Area: ${specificArea}`);
    }

    const contextSection = contextInfo.length > 0 
        ? `\n**Context Information:**\n${contextInfo.map(info => `- ${info}`).join('\n')}\n`
        : '';

    // The main prompt structure
    return `
        **Role and Goal:**
        You are an expert business consultant, strategist, and advisor with over 20 years of experience helping businesses across all industries. Your task is to provide comprehensive, actionable, and insightful answers to business questions. The output MUST strictly follow the specified tagged format for frontend rendering.

        **IMPORTANT LANGUAGE REQUIREMENT:**
        Your ENTIRE response must be written in ${language}. Every single word, sentence, and explanation must be in ${language}. Do not mix languages.

        **Question to Answer:**
        "${question}"
        ${contextSection}
        **Required Output Structure & Format Instructions:**
        Generate a comprehensive business answer following this EXACT structure. You MUST use the specified ~TAGS~ for each part. Each tagged element must be on a new line. Do NOT add any extra markdown, titles, or explanations. The entire output must be only the tagged text. Remember: Write everything in ${language}.

        --- START OF REQUIRED FORMAT ---
        ~H~ Business Answer: ${question.length > 50 ? question.substring(0, 50) + '...' : question}

        ~S_SUB~ Executive Summary
        ~P~ [Provide a concise, high-level answer to the question in 2-3 sentences. This should be the key takeaway.]

        ~S_SUB~ Detailed Analysis
        ~P~ [Provide a comprehensive analysis of the question. Break down the key components, considerations, and factors involved. Be specific and actionable.]

        ~S_SUB~ Strategic Recommendations
        ~REC_LIST_START~
        ~REC~ [First specific, actionable recommendation with clear steps]
        ~REC~ [Second specific, actionable recommendation with clear steps]
        ~REC~ [Third specific, actionable recommendation with clear steps]
        ~REC_LIST_END~

        ~S_SUB~ Implementation Steps
        ~STEP_LIST_START~
        ~STEP~ Step 1: [First concrete action step with timeline if applicable]
        ~STEP~ Step 2: [Second concrete action step with timeline if applicable]
        ~STEP~ Step 3: [Third concrete action step with timeline if applicable]
        ~STEP_LIST_END~

        ~S_SUB~ Key Considerations & Risks
        ~RISK_LIST_START~
        ~RISK~ [Important risk or consideration to be aware of]
        ~RISK~ [Another important risk or consideration]
        ~RISK~ [Third important risk or consideration]
        ~RISK_LIST_END~

        ~S_SUB~ Success Metrics & KPIs
        ~P~ [Explain how to measure success for this business question/strategy. Include specific metrics and benchmarks where applicable.]

        ~S_SUB~ Additional Resources & Next Steps
        ~P~ [Suggest additional resources, tools, or next steps the user should consider. Be specific about what they should research or implement next.]

        ~S_SUB~ Expert Insight
        ~INSIGHT~ [Provide a valuable insight or industry best practice that adds extra value to the answer. This should be something that demonstrates deep expertise.]

        **OPTIONAL: If your answer would benefit from tabular data, you can include tables using this format:**
        ~S_SUB~ Data Analysis (or relevant table title)
        ~TABLE_START~
        ~TABLE_HEADER~ Column 1 | Column 2 | Column 3
        ~TABLE_ROW~ Data 1 | Data 2 | Data 3
        ~TABLE_ROW~ Data 4 | Data 5 | Data 6
        ~TABLE_END~

        --- END OF REQUIRED FORMAT ---

        **Important Guidelines:**
        1. Make your answer comprehensive but practical
        2. Include specific examples where relevant
        3. Consider the business context provided
        4. Ensure recommendations are actionable and realistic
        5. Use professional business language
        6. Focus on value creation and problem-solving
        7. Consider both short-term and long-term implications
        8. Address potential challenges and how to overcome them
        9. Use tables when presenting comparative data, metrics, timelines, or structured information
        10. Keep table data concise but informative - each cell should contain meaningful information

        **When to Use Tables:**
        - Comparing different options or strategies
        - Showing financial projections or metrics
        - Presenting timelines or phases
        - Displaying market analysis data
        - Listing features vs benefits
        - Showing before/after scenarios

        Generate the business answer now, following the exact format above.
        `;
};
