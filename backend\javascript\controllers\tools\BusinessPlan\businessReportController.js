// controllers/businessPlan/PlanGeneratorController.js
import { generateContent } from '../../../services/geminiBusinessService.js';
import { buildFullBusinessPlanPrompt } from './PlanGenratorPrompts.js';
import axios from 'axios';
import User from '../../../models/User.js';

const fetchMarketData = async (query) => {
    if (!query) return [];
    try {
        const response = await axios.request({
            method: 'GET',
            url: 'https://dataservices-market-research-library-v1.p.rapidapi.com/api.trade.gov/v2/market_research_library/search',
            params: { q: query, limit: 5 },
            headers: {
                'x-rapidapi-key': process.env.RAPIDAPI_KEY,
                'x-rapidapi-host': 'dataservices-market-research-library-v1.p.rapidapi.com'
            }
        });
        return response.data?.results || [];
    } catch (error) {
        console.error('RapidAPI Market Data Request Failed:', error.message);
        return [];
    }
};

export const generateFullBusinessReport = async (req, res) => {
    // 1. Fetch user and validate form data
    const user = await User.findById(req.user.id);
    if (!user) {
        return res.status(404).json({ error: 'User not found.' });
    }

    const formData = req.body;
    const { planType, idea, audience, profit, problem, language, period, budget, industry } = formData;

    if (!planType || !language || !period) {
        return res.status(400).json({ error: 'Core planning parameters are missing.' });
    }
    if (planType === 'user' && (!idea || !audience || !profit || !problem)) {
        return res.status(400).json({ error: 'A complete business context is required to generate a plan.' });
    } else if (planType === 'ai' && (!budget || !industry)) {
        return res.status(400).json({ error: 'Investment Budget and Industry are required for the AI to generate an idea.' });
    }

    // 2. Check usage limit
    const sub = user.subscription || {};
    const userPlanName = sub.planName || 'Starter';
    const STARTER_LIMIT = 2;
    const PRO_LIMIT = 20;

    let canGenerate = true;

    if (userPlanName === 'Starter') {
        if ((sub.freeTierBusinessPlanCount || 0) >= STARTER_LIMIT) {
            canGenerate = false;
        }
    } else if (userPlanName === 'Pro') {
        if ((sub.proTierBusinessPlanCount || 0) >= PRO_LIMIT) {
            canGenerate = false;
        }
    }

    if (!canGenerate) {
        return res.status(403).json({ error: `You have reached your limit of ${userPlanName === 'Starter' ? STARTER_LIMIT : PRO_LIMIT} plans for the ${userPlanName} plan. Please upgrade for more.` });
    }

    // 3. Generate the report
    try {
        console.log(`[CONTROLLER] Generating new report for planType: "${planType}" for user: ${user.email}`);
        
        const marketQuery = planType === 'ai' ? industry : idea;
        const marketData = await fetchMarketData(marketQuery);
        
        const finalPrompt = buildFullBusinessPlanPrompt(formData, marketData);
        
        const fullReportText = await generateContent(finalPrompt);

        // 4. Increment count and save user (only on success)
        if (userPlanName === 'Starter') {
            user.subscription.freeTierBusinessPlanCount = (user.subscription.freeTierBusinessPlanCount || 0) + 1;
        } else if (userPlanName === 'Pro') {
            user.subscription.proTierBusinessPlanCount = (user.subscription.proTierBusinessPlanCount || 0) + 1;
        }

        await user.save();
        console.log(`[CONTROLLER] Successfully incremented plan count for user: ${user.email}`);

        // 5. Send response with report and updated subscription
        res.status(200).json({ 
            fullBusinessReport: fullReportText,
            subscription: user.subscription // Include the updated subscription for instant UI update
        });

    } catch (error) {
        console.error(`Controller Error during report generation for user ${user.email}:`, error.message);
        res.status(500).json({
            error: error.message || 'An internal server error occurred while generating the report.'
        });
    }
};