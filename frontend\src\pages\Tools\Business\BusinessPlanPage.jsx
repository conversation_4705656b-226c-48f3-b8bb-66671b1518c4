import React, { useState, useCallback, Suspense, useMemo, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { v4 as uuidv4 } from 'uuid';
import cn from 'classnames';
import { updateUserSubscription } from '../../../store/features/auth/authSlice';

// Child Components (Steps of the form)
import StepFiveDisplayPlan from './components/steps/step5/StepFiveDisplayPlan';
const StepOne = React.lazy(() => import('./components/steps/step1/StepOne'));
const StepTwoAI = React.lazy(() => import('./components/steps/step2/StepTwoAI'));
const StepTwoMyIdea = React.lazy(() => import('./components/steps/step2/StepTwoMyIdea'));
const StepThreePeriod = React.lazy(() => import('./components/steps/step3/StepThreePeriod'));
const StepFourLanguage = React.lazy(() => import('./components/steps/step4/StepFourLanguage'));

// --- Constants ---
const TOTAL_STEPS = 4;
const DISPLAY_STEP = TOTAL_STEPS + 1;
const PLAN_EXPIRATION_MINUTES = 30;

// --- UI Components ---
const StepLoader = () => (
    <div className="flex justify-center items-center h-48">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-sky-400"></div>
    </div>
);

const BusinessPlanPage = () => {
    const { planId } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    // --- Component State ---
    const [currentStep, setCurrentStep] = useState(1);
    const [formData, setFormData] = useState({});
    const [isLoadingReport, setIsLoadingReport] = useState(false);
    const [reportData, setReportData] = useState(null);
    const [reportError, setReportError] = useState(null);
    const [isRetryable, setIsRetryable] = useState(false);
    const [isSaved, setIsSaved] = useState(false);
    const [isSaving, setIsSaving] = useState(false); // For the "Save" button's loading state

    const isDisplayStep = useMemo(() => currentStep === DISPLAY_STEP, [currentStep]);

    // --- API & LOGIC FUNCTIONS ---

    /**
     * Calls the backend to save the currently displayed business plan.
     * Uses the new '/api/saved-plans' endpoint.
     */
    const handleSavePlan = useCallback(async () => {
        if (!reportData || !planId || !formData) {
            alert('Cannot save. Plan data is incomplete.');
            return;
        }

        setIsSaving(true);
        const token = localStorage.getItem('authToken');

        if (!token) {
            alert('Authentication error. Please log in again to save your plan.');
            setIsSaving(false);
            return;
        }

        try {
            // UPDATED: Calls the new, dedicated endpoint for saving plans.
            const response = await fetch('/api/saved-plans', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    planId: planId,
                    formData: formData,
                    reportData: reportData,
                }),
            });

            const result = await response.json();
            if (!response.ok) throw new Error(result.error || 'Failed to save the plan.');

            console.log('Plan saved successfully:', result.message);
            setIsSaved(true);

            // Redirect to the saved plans page after a brief delay for user feedback.
            setTimeout(() => {
                navigate('/app/business/saved');
            }, 1200);

        } catch (error) {
            console.error('Save failed:', error);
            alert(`Error: ${error.message}`);
            setIsSaving(false); // Allow user to try again on failure
        }
    }, [reportData, planId, formData, navigate]);

    /**
     * Generates a new plan using the AI service and stores it temporarily in localStorage.
     */
    const generateNewReport = useCallback(async (currentFormData) => {
        setIsLoadingReport(true);
        setReportData(null);
        setReportError(null);
        setIsRetryable(false);

        try {
            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('Authentication required. Please log in and try again.');
            }

            const response = await fetch('/api/business-plan/full-report', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(currentFormData),
            });
            const data = await response.json();
            if (!response.ok) throw new Error(data.error || 'Failed to generate the report.');

            // Update user subscription in Redux if returned from backend
            if (data.subscription) {
                dispatch(updateUserSubscription(data.subscription));
            }

            const newPlanId = uuidv4();
            const reportContent = { ...data, language: currentFormData.language || 'English' };
            const expiresAt = new Date().getTime() + PLAN_EXPIRATION_MINUTES * 60 * 1000;

            // Store temporarily in the browser so a refresh doesn't lose the data
            const itemToCache = { data: reportContent, expiresAt, formData: currentFormData };
            localStorage.setItem(`business-plan-${newPlanId}`, JSON.stringify(itemToCache));

            setReportData(reportContent);
            setFormData(currentFormData);
            navigate(`/app/business/view/${newPlanId}`, { replace: true });
        } catch (err) {
            setReportError(err.message);
            setIsRetryable(true);
        } finally {
            setIsLoadingReport(false);
        }
    }, [navigate]);

    /**
     * This effect runs when the component mounts with a `planId` in the URL.
     * It first checks for a temporary plan in localStorage. If not found, it
     * falls back to fetching the permanently saved plan from the new API endpoint.
     */
    useEffect(() => {
        const fetchPlanData = async (id) => {
            setCurrentStep(DISPLAY_STEP);
            setIsLoadingReport(true);
            setReportError(null);

            try {
                // 1. Check for a temporary, unsaved plan in the browser's localStorage.
                const cachedItemJSON = localStorage.getItem(`business-plan-${id}`);
                if (cachedItemJSON) {
                    const cachedItem = JSON.parse(cachedItemJSON);
                    if (cachedItem.expiresAt && new Date().getTime() > cachedItem.expiresAt) {
                        localStorage.removeItem(`business-plan-${id}`);
                        throw new Error("This temporary plan has expired. Please create a new one.");
                    }
                    setReportData(cachedItem.data);
                    if(cachedItem.formData) setFormData(cachedItem.formData);
                    return; // Successfully loaded from cache, no need to call API.
                }

                // 2. If not in cache, fetch the permanently saved plan from the API.
                const token = localStorage.getItem('authToken');
                if (!token) throw new Error("You must be logged in to view this plan.");

                // UPDATED: Calls the new endpoint to get a single saved plan.
                const response = await fetch(`/api/saved-plans/${id}`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (!response.ok) {
                    throw new Error("Plan not found or you don't have permission to view it.");
                }

                const savedPlanData = await response.json();
                setReportData(savedPlanData.reportData);
                setFormData(savedPlanData.formData);
                setIsSaved(true); // Mark as already saved because it came from the database.

            } catch (err) {
                setReportError(err.message);
            } finally {
                setIsLoadingReport(false);
            }
        };

        if (planId && !reportData) {
            fetchPlanData(planId);
        }
    }, [planId, reportData]);


    // --- Step Navigation Handlers ---
    const handleStep1Select = useCallback((planType) => { setFormData({ planType }); setCurrentStep(2); }, []);
    const updateFormData = useCallback((stepData) => setFormData(prev => ({ ...prev, ...stepData })), []);
    const handleNext = useCallback(() => setCurrentStep(prev => prev + 1), []);
    const handleBack = useCallback(() => setCurrentStep(prev => prev - 1), []);
    const handleGeneratePlan = useCallback(() => { setIsLoadingReport(true); setCurrentStep(DISPLAY_STEP); generateNewReport(formData) }, [formData, generateNewReport]);
    const handleReset = useCallback(() => { navigate('/app/business/create', { replace: true }); }, [navigate]);
    const handleRetry = useCallback(() => { generateNewReport(formData); }, [formData, generateNewReport]);

    /**
     * Renders the appropriate component based on the current step.
     */
    const renderStep = () => {
        if (isDisplayStep) {
            return (
                <StepFiveDisplayPlan 
                    isLoading={isLoadingReport} 
                    error={reportError} 
                    reportData={reportData} 
                    language={reportData?.language || 'English'} 
                    onReset={handleReset}
                    onRetry={isRetryable ? handleRetry : null}
                    onSave={handleSavePlan}
                    isSaved={isSaved}
                    isSaving={isSaving}
                />
            );
        }
        
        switch (currentStep) {
            case 1: return <StepOne onSelectOption={handleStep1Select} />;
            case 2: return formData.planType === 'ai' ? <StepTwoAI onDataChange={updateFormData} initialData={formData} /> : <StepTwoMyIdea onDataChange={updateFormData} initialData={formData} />;
            case 3: return <StepThreePeriod onDataChange={updateFormData} initialData={formData} />;
            case 4: return <StepFourLanguage onDataChange={updateFormData} initialData={formData} />;
            default: return <StepOne onSelectOption={handleStep1Select} />;
        }
    };

    return (
        <div className="container justify-center items-center mx-auto text-white w-full min-h-full flex flex-col">
            <main className={cn("w-full flex-grow flex flex-col", { "max-w-3xl justify-center": !isDisplayStep, "max-w-5xl justify-start pt-8 md:pt-12": isDisplayStep })}>
                <div className={cn('bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 transition-all duration-300 w-full', { 'bg-slate-900/30 p-4 sm:p-6 md:p-8': isDisplayStep, 'min-h-[400px] p-6 md:p-10 flex flex-col justify-between mt-10': !isDisplayStep })}>
                    <div className={cn("flex-grow flex items-center", { "justify-center": !isDisplayStep })}>
                        <Suspense fallback={<StepLoader />}>
                            {renderStep()}
                        </Suspense>
                    </div>
                    {!isDisplayStep && (
                        <nav className="flex justify-between w-full mt-6">
                            <button onClick={handleBack} disabled={currentStep === 1} className="px-6 py-2 bg-slate-600 hover:bg-slate-500 text-white font-semibold rounded-lg disabled:opacity-50 disabled:cursor-not-allowed">Back</button>
                            {currentStep < TOTAL_STEPS ? (
                                <button onClick={handleNext} className="px-6 py-2 bg-sky-600 hover:bg-sky-500 text-white font-semibold rounded-lg">Next</button>
                            ) : (
                                <button onClick={handleGeneratePlan} className="px-6 py-2 bg-green-600 hover:bg-green-500 text-white font-bold rounded-lg shadow-lg">Generate Plan</button>
                            )}
                        </nav>
                    )}
                </div>
            </main>
        </div>
    );
};

export default BusinessPlanPage;