import User from '../../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    STARTER_BUSINESS_PLAN_LIMIT,
    PRO_BUSINESS_PLAN_LIMIT,
} from './planConfig.js';

/**
 * @desc    Increment business plan generation count for applicable plans
 * @route   POST /api/users/me/increment-plan-count
 * @access  Private
 */
export const incrementBusinessPlanCount = async (req, res) => {
    try {
        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        user = ensureSubscription(user);

        const { planName } = user.subscription;
        let limitReached = false;
        let message = "Plan count successfully incremented.";
        let countIncremented = false;

        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            const currentCount = user.subscription.freeTierBusinessPlanCount || 0;
            if (currentCount >= STARTER_BUSINESS_PLAN_LIMIT) {
                limitReached = true;
                message = "Starter plan business plan limit reached. Please upgrade to generate more.";
            } else {
                user.subscription.freeTierBusinessPlanCount = currentCount + 1;
                countIncremented = true;
            }
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            const currentCount = user.subscription.proTierBusinessPlanCount || 0;
            if (currentCount >= PRO_BUSINESS_PLAN_LIMIT) {
                limitReached = true;
                message = "Pro plan business plan limit reached.";
            } else {
                user.subscription.proTierBusinessPlanCount = currentCount + 1;
                countIncremented = true;
            }
        } else {
            return res.status(400).json({ message: "User is not on a plan that supports business plan generation." });
        }

        if (limitReached) {
            return res.status(403).json({ message });
        }

        if (countIncremented) {
            await user.save();
        }

        res.json({
            message,
            subscription: user.subscription,
        });

    } catch (error) {
        console.error('Increment Business Plan Count Error:', error);
        res.status(500).json({ message: 'Server error while incrementing business plan count.' });
    }
};