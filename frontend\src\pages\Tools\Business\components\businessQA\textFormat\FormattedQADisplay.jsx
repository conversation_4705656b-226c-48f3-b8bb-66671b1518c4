import React, { useState } from 'react';
import { 
    FiCopy, 
    FiDownload, 
    FiRefreshCw, 
    FiCheckCircle, 
    FiTarget, 
    FiAlertTriangle, 
    FiTrendingUp, 
    FiList 
} from 'react-icons/fi';

//==================================================================
//  1. Utility Functions
//==================================================================

const copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        console.error('Failed to copy text: ', err);
        return false;
    }
};

const downloadAsText = (content, filename = 'business-qa-answer.txt') => {
    const element = document.createElement('a');
    const file = new Blob([content], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
};

//==================================================================
//  2. Styled Components for Different Content Types
//==================================================================

const SectionHeader = ({ children }) => (
    <h2 className="text-xl font-bold text-blue-400 mb-4 flex items-center">
        <FiTarget className="w-5 h-5 mr-2" />
        {children}
    </h2>
);

const SubSectionHeader = ({ children }) => (
    <h3 className="text-lg font-semibold text-slate-200 mb-3 border-l-4 border-blue-500 pl-3">
        {children}
    </h3>
);

const Paragraph = ({ children }) => (
    <p className="text-slate-300 leading-relaxed mb-4">
        {children}
    </p>
);

const RecommendationList = ({ children }) => (
    <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 mb-4">
        <div className="flex items-center mb-3">
            <FiCheckCircle className="w-5 h-5 text-green-400 mr-2" />
            <span className="font-semibold text-green-400">Strategic Recommendations</span>
        </div>
        <ul className="space-y-2">
            {children}
        </ul>
    </div>
);

const StepList = ({ children }) => (
    <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 mb-4">
        <div className="flex items-center mb-3">
            <FiList className="w-5 h-5 text-blue-400 mr-2" />
            <span className="font-semibold text-blue-400">Implementation Steps</span>
        </div>
        <ol className="space-y-2">
            {children}
        </ol>
    </div>
);

const RiskList = ({ children }) => (
    <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-4">
        <div className="flex items-center mb-3">
            <FiAlertTriangle className="w-5 h-5 text-yellow-400 mr-2" />
            <span className="font-semibold text-yellow-400">Key Considerations & Risks</span>
        </div>
        <ul className="space-y-2">
            {children}
        </ul>
    </div>
);

const InsightBox = ({ children }) => (
    <div className="bg-purple-500/10 border border-purple-500/30 rounded-lg p-4 mb-4">
        <div className="flex items-center mb-3">
            <FiCheckCircle className="w-5 h-5 text-purple-400 mr-2" />
            <span className="font-semibold text-purple-400">Expert Insight</span>
        </div>
        <p className="text-slate-300 italic">
            {children}
        </p>
    </div>
);

const RecommendationItem = ({ children }) => (
    <li className="flex items-start">
        <FiTrendingUp className="w-4 h-4 text-green-400 mr-2 mt-1 flex-shrink-0" />
        <span className="text-slate-300">{children}</span>
    </li>
);

const StepItem = ({ children, stepNumber }) => (
    <li className="flex items-start">
        <span className="bg-blue-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
            {stepNumber}
        </span>
        <span className="text-slate-300">{children}</span>
    </li>
);

const RiskItem = ({ children }) => (
    <li className="flex items-start">
        <FiAlertTriangle className="w-4 h-4 text-yellow-400 mr-2 mt-1 flex-shrink-0" />
        <span className="text-slate-300">{children}</span>
    </li>
);

//==================================================================
//  3. Main Parser Function
//==================================================================

const parseQAContent = (content) => {
    const lines = content.split('\n').filter(line => line.trim() !== '');
    const elements = [];
    let currentRecommendations = [];
    let currentSteps = [];
    let currentRisks = [];
    let stepCounter = 1;
    let inRecommendationList = false;
    let inStepList = false;
    let inRiskList = false;

    lines.forEach((line, index) => {
        const trimmedLine = line.trim();

        // Handle list endings
        if (trimmedLine === '~REC_LIST_END~') {
            if (currentRecommendations.length > 0) {
                elements.push(
                    <RecommendationList key={`rec-list-${index}`}>
                        {currentRecommendations}
                    </RecommendationList>
                );
                currentRecommendations = [];
            }
            inRecommendationList = false;
            return;
        }

        if (trimmedLine === '~STEP_LIST_END~') {
            if (currentSteps.length > 0) {
                elements.push(
                    <StepList key={`step-list-${index}`}>
                        {currentSteps}
                    </StepList>
                );
                currentSteps = [];
            }
            inStepList = false;
            stepCounter = 1;
            return;
        }

        if (trimmedLine === '~RISK_LIST_END~') {
            if (currentRisks.length > 0) {
                elements.push(
                    <RiskList key={`risk-list-${index}`}>
                        {currentRisks}
                    </RiskList>
                );
                currentRisks = [];
            }
            inRiskList = false;
            return;
        }

        // Handle list beginnings
        if (trimmedLine === '~REC_LIST_START~') {
            inRecommendationList = true;
            return;
        }

        if (trimmedLine === '~STEP_LIST_START~') {
            inStepList = true;
            return;
        }

        if (trimmedLine === '~RISK_LIST_START~') {
            inRiskList = true;
            return;
        }

        // Handle content within lists
        if (inRecommendationList && trimmedLine.startsWith('~REC~')) {
            currentRecommendations.push(
                <RecommendationItem key={`rec-${index}`}>
                    {trimmedLine.substring(5).trim()}
                </RecommendationItem>
            );
            return;
        }

        if (inStepList && trimmedLine.startsWith('~STEP~')) {
            currentSteps.push(
                <StepItem key={`step-${index}`} stepNumber={stepCounter}>
                    {trimmedLine.substring(6).trim()}
                </StepItem>
            );
            stepCounter++;
            return;
        }

        if (inRiskList && trimmedLine.startsWith('~RISK~')) {
            currentRisks.push(
                <RiskItem key={`risk-${index}`}>
                    {trimmedLine.substring(6).trim()}
                </RiskItem>
            );
            return;
        }

        // Handle other content types
        if (trimmedLine.startsWith('~H~')) {
            elements.push(
                <SectionHeader key={index}>
                    {trimmedLine.substring(3).trim()}
                </SectionHeader>
            );
        } else if (trimmedLine.startsWith('~S_SUB~')) {
            elements.push(
                <SubSectionHeader key={index}>
                    {trimmedLine.substring(7).trim()}
                </SubSectionHeader>
            );
        } else if (trimmedLine.startsWith('~P~')) {
            elements.push(
                <Paragraph key={index}>
                    {trimmedLine.substring(3).trim()}
                </Paragraph>
            );
        } else if (trimmedLine.startsWith('~INSIGHT~')) {
            elements.push(
                <InsightBox key={index}>
                    {trimmedLine.substring(9).trim()}
                </InsightBox>
            );
        } else if (!trimmedLine.startsWith('~') && trimmedLine.length > 0) {
            // Handle untagged content as regular paragraphs
            elements.push(
                <Paragraph key={index}>
                    {trimmedLine}
                </Paragraph>
            );
        }
    });

    return elements;
};

//==================================================================
//  4. Main Component
//==================================================================

const FormattedQADisplay = ({ answerText, onReset }) => {
    const [copySuccess, setCopySuccess] = useState(false);

    const handleCopy = async () => {
        const success = await copyToClipboard(answerText);
        if (success) {
            setCopySuccess(true);
            setTimeout(() => setCopySuccess(false), 2000);
        }
    };

    const handleDownload = () => {
        downloadAsText(answerText, 'business-qa-answer.txt');
    };

    const parsedContent = parseQAContent(answerText);

    return (
        <div className="w-full bg-slate-900/50 border border-slate-700 rounded-2xl p-6 md:p-8 animate-fade-in">
            {/* Header with action buttons */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                <h2 className="text-2xl font-bold text-white">Business Answer</h2>
                <div className="flex gap-2">
                    <button
                        onClick={handleCopy}
                        className="flex items-center px-3 py-2 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white rounded-lg transition-all duration-200 text-sm"
                    >
                        {copySuccess ? <FiCheckCircle className="w-4 h-4 mr-1" /> : <FiCopy className="w-4 h-4 mr-1" />}
                        {copySuccess ? 'Copied!' : 'Copy'}
                    </button>
                    <button
                        onClick={handleDownload}
                        className="flex items-center px-3 py-2 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white rounded-lg transition-all duration-200 text-sm"
                    >
                        <FiDownload className="w-4 h-4 mr-1" />
                        Download
                    </button>
                    <button
                        onClick={onReset}
                        className="flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 text-sm"
                    >
                        <FiRefreshCw className="w-4 h-4 mr-1" />
                        Ask Another
                    </button>
                </div>
            </div>

            {/* Formatted content */}
            <div className="prose prose-invert max-w-none">
                {parsedContent}
            </div>
        </div>
    );
};

export default FormattedQADisplay;