// routes/userRoutes.js
import express from 'express';
import {
  incrementPdfUploadCount,
  incrementMessageCount,
  incrementBusinessPlanCount,
  incrementInvestorPitchCount
} from '../../controllers/common/user/userActionsController.js';
import { protect } from '../../middleware/authMiddleware.js';

const router = express.Router();

// Route to increment the PDF upload count for the authenticated user
router.post('/me/increment-upload-count', protect, incrementPdfUploadCount);

// Route to increment the chat message count for the authenticated user
router.post('/me/increment-message-count', protect, incrementMessageCount);

// Route to increment the business plan generation count for the authenticated user
router.post('/me/increment-plan-count', protect, incrementBusinessPlanCount);

// Route to increment the investor pitch generation count for the authenticated user
router.post('/me/increment-pitch-count', protect, incrementInvestorPitchCount);

export default router;