// node_gemini_summarizer/controllers/auth/index.js
import { registerUser } from './auth.register.js';
import { loginUser } from './auth.login.js';
import { verifyUserEmail } from './auth.verify.js';
import { resendUserVerificationCode } from './auth.resendCode.js';
import { getCurrentUser } from './auth.currentUser.js'; // <--- IMPORT IT

export {
    registerUser as register,
    loginUser as login,
    verifyUserEmail as verifyEmail,
    resendUserVerificationCode as resendVerificationCode,
    getCurrentUser // <--- EXPORT IT (no need to alias if name is fine)
};