{"name": "express-static-gzip", "version": "3.0.0", "description": "simple wrapper on top of express.static, that allows serving pre-gziped files", "main": "index.js", "types": "index.d.ts", "files": ["util/", "LICENSE.md", "README.md", "index.d.ts"], "scripts": {"test": "mocha test/*.spec.js", "test:coverage": "nyc --reporter=lcov mocha"}, "keywords": ["express", "static", "gzip", "brotli", "serve", "compression"], "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/tkoenig89/express-static-gzip"}, "license": "MIT", "dependencies": {"mime-types": "^3.0.1", "parseurl": "^1.3.3", "serve-static": "^2.2.0"}, "devDependencies": {"chai": "^4.1.2", "express": "^5.1.0", "mocha": "^5.2.0", "nyc": "^15.1.0", "request": "^2.87.0"}}