// node_gemini_summarizer/controllers/auth/auth.helpers.js
import jwt from 'jsonwebtoken';
import { JWT_SECRET, JWT_EXPIRY_DURATION } from './auth.config.js'; // Ensure these are correctly defined in auth.config.js

export const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
};

export const generateAuthToken = (user) => {
  const userId = user.id || user._id.toString();

  const payload = {
    id: userId,
    // Note: While you can include email, name, etc. in the JWT payload,
    // it's often preferred to keep the token small and fetch user details
    // separately after authentication if needed for display.
    // However, if your 'protect' middleware relies on these, keep them.
    // For this example, we'll assume 'id' is primary.
  };
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRY_DURATION });
};

export const formatUserResponse = (user) => {
    const userObject = user.toObject ? user.toObject() : user; // Handle Mongoose doc or plain object
    return {
        id: userObject._id.toString(), // Ensure it's a string
        email: userObject.email,
        name: userObject.name,
        isVerified: userObject.isVerified,
        subscription: userObject.subscription, // <<< CRITICAL: Include the subscription object
        // Do NOT include password or other sensitive fields
    };
};