// File Path: node_gemini_summarizer/backend/javascrupt/config/appConfig.js
import dotenv from 'dotenv';
import path from 'path';

// This ensures .env is loaded if it hasn't been already by server.js for example.
// The global process.env will reflect the first successful load.
const envPath = path.resolve(process.cwd(), '.env');
if (!process.env.GEMINI_API_KEY) { // Only attempt to load if not already set
    dotenv.config({ path: envPath });
}

export const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
export const NODE_ENV = process.env.NODE_ENV || 'development';

if (!GEMINI_API_KEY && NODE_ENV !== 'test') {
    console.error("[AppConfig FATAL ERROR] GEMINI_API_KEY is not defined. Check your .env file or environment variables.");
}