// src/controllers/Tools/Business/investorPitchController.js

import { generateContent } from '../../../services/geminiBusinessService.js';
import { buildInvestorPitchPrompt } from './investorPitchPrompts.js';
import User from '../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    STARTER_INVESTOR_PITCH_LIMIT,
    PRO_INVESTOR_PITCH_LIMIT,
} from '../../common/user/limit/planConfig.js';

/**
 * Controller to handle the generation of an investor pitch.
 */
export const generateInvestorPitch = async (req, res) => {
    // 1. Extract and validate form data from the request body
    const {
        projectName,
        industry,
        projectDescription,
        problemStatement,
        solution,
        targetAudience,
        competition,
        pitchObjective,
        growthPlan,
        toneOfVoice
    } = req.body;

    if (!projectName || !projectDescription || !problemStatement || !solution || !targetAudience || !competition || !pitchObjective || !growthPlan || !toneOfVoice) {
        return res.status(400).json({ 
            error: 'Missing required fields. Please ensure all sections of the form are completed.' 
        });
    }

    try {
        console.log(`[PITCH_CONTROLLER] Generating investor pitch for project: "${projectName}"`);

        // 2. Check user limits before generating
        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        user = ensureSubscription(user);
        const { planName } = user.subscription;

        // Check limits based on plan
        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            const currentCount = user.subscription.freeTierInvestorPitchCount || 0;
            if (currentCount >= STARTER_INVESTOR_PITCH_LIMIT) {
                return res.status(403).json({
                    error: `Starter plan investor pitch limit reached (${STARTER_INVESTOR_PITCH_LIMIT}). Please upgrade to generate more pitches.`
                });
            }
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            const currentCount = user.subscription.proTierInvestorPitchCount || 0;
            if (currentCount >= PRO_INVESTOR_PITCH_LIMIT) {
                return res.status(403).json({
                    error: `Pro plan investor pitch limit reached (${PRO_INVESTOR_PITCH_LIMIT}).`
                });
            }
        }

        // 3. Build the advanced prompt using the dedicated prompt-builder function
        const pitchPrompt = buildInvestorPitchPrompt(req.body);

        // 4. Call the generic Gemini service to generate the content
        const generatedPitch = await generateContent(pitchPrompt);

        // 5. Increment the usage count after successful generation
        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            user.subscription.freeTierInvestorPitchCount = (user.subscription.freeTierInvestorPitchCount || 0) + 1;
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            user.subscription.proTierInvestorPitchCount = (user.subscription.proTierInvestorPitchCount || 0) + 1;
        }
        await user.save();

        // 6. Send the successful response back to the client
        res.status(200).json({
            generatedPitch,
            subscription: user.subscription // Include updated subscription for instant UI update
        });

    } catch (error) {
        console.error(`[PITCH_CONTROLLER] Error during pitch generation:`, error.message);
        res.status(500).json({
            error: 'An internal server error occurred while generating the investor pitch.'
        });
    }
};