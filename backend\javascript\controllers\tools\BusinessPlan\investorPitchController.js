// src/controllers/Tools/Business/investorPitchController.js

import { generateContent } from '../../../services/geminiBusinessService.js';
import { buildInvestorPitchPrompt } from './investorPitchPrompts.js';

/**
 * Controller to handle the generation of an investor pitch.
 */
export const generateInvestorPitch = async (req, res) => {
    // 1. Extract and validate form data from the request body
    const {
        projectName,
        industry,
        projectDescription,
        problemStatement,
        solution,
        targetAudience,
        competition,
        pitchObjective,
        growthPlan,
        toneOfVoice
    } = req.body;

    if (!projectName || !projectDescription || !problemStatement || !solution || !targetAudience || !competition || !pitchObjective || !growthPlan || !toneOfVoice) {
        return res.status(400).json({ 
            error: 'Missing required fields. Please ensure all sections of the form are completed.' 
        });
    }

    try {
        console.log(`[PITCH_CONTROLLER] Generating investor pitch for project: "${projectName}"`);
        
        // 2. Build the advanced prompt using the dedicated prompt-builder function
        const pitchPrompt = buildInvestorPitchPrompt(req.body);

        // 3. Call the generic Gemini service to generate the content
        const generatedPitch = await generateContent(pitchPrompt);

        // 4. Send the successful response back to the client
        res.status(200).json({ generatedPitch });

    } catch (error) {
        console.error(`[PITCH_CONTROLLER] Error during pitch generation:`, error.message);
        res.status(500).json({
            error: 'An internal server error occurred while generating the investor pitch.'
        });
    }
};