// models/User.js
import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

// Define the schema for subscription details
const subscriptionSchema = new mongoose.Schema({
  planName: {
    type: String,
    enum: ['Starter', 'Pro', 'Enterprise', null],
    default: 'Starter',
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'cancelled', 'pending_payment'],
    default: 'active',
  },
  paypalOrderId: {
    type: String,
    required: false,
  },
  startDate: {
    type: Date,
    default: null,
  },
  endDate: {
    type: Date,
    default: null,
  },
  lastPaymentDate: {
    type: Date,
    default: null,
  },
  freeTierUploadCount: {
    type: Number,
    default: 0,
  },
  proTierUploadCount: {
    type: Number,
    default: 0,
  },
  // --- ADDED FIELDS ---
  freeTierBusinessPlanCount: {
    type: Number,
    default: 0,
  },
  proTierBusinessPlanCount: {
    type: Number,
    default: 0,
  },
  freeTierMessageCount: {
    type: Number,
    default: 0,
  },
  proTierMessageCount: {
    type: Number,
    default: 0,
  },
  // --- INVESTOR PITCH COUNTS ---
  freeTierInvestorPitchCount: {
    type: Number,
    default: 0,
  },
  proTierInvestorPitchCount: {
    type: Number,
    default: 0,
  }
}, { _id: false });

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: false,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    match: [/\S+@\S+\.\S+/, 'is invalid'],
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  subscription: {
    type: subscriptionSchema,
    default: () => ({
      planName: 'Starter',
      status: 'active',
      startDate: new Date(),
      freeTierUploadCount: 0,
      proTierUploadCount: 0,
      // --- INITIALIZE ADDED FIELDS ---
      freeTierBusinessPlanCount: 0,
      proTierBusinessPlanCount: 0,
      freeTierMessageCount: 0,
      proTierMessageCount: 0,
      freeTierInvestorPitchCount: 0,
      proTierInvestorPitchCount: 0,
    })
  },
}, { timestamps: true });

// Hash password before saving
UserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    return next();
  }
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (err) {
    next(err);
  }
});

// Method to compare password for login
UserSchema.methods.comparePassword = async function (candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    console.error("Error comparing password:", error);
    return false;
  }
};

const User = mongoose.model('User', UserSchema);

export default User;