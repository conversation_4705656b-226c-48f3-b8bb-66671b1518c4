// src/controllers/Tools/Business/investorPitchPrompts.js

/**
 * Constructs an advanced, detailed prompt for generating an investor pitch,
 * now with structured tags for rich component-based rendering on the frontend.
 * @param {object} formData - The validated data from the frontend form.
 * @returns {string} The complete prompt to be sent to the Gemini AI.
 */
export const buildInvestorPitchPrompt = (formData) => {
    const {
        projectName,
        industry,
        projectDescription,
        problemStatement,
        solution,
        targetAudience,
        competition,
        pitchObjective,
        fundingAmount,
        growthPlan,
        toneOfVoice,
    } = formData;

    // The content for the ~ASK~ tag is generated dynamically.
    const theAskContent = fundingAmount
        ? `Our ask is for ${fundingAmount} to be primarily allocated towards product development, aggressive marketing, and key team hires. This investment will accelerate our growth and capture a significant market share.`
        : `Our primary objective is to ${pitchObjective}. We are seeking strategic partners who align with our vision and can help us achieve this goal.`;

    // The main prompt structure
    return `
        **Role and Goal:**
        You are an expert startup consultant and world-class copywriter. Your task is to write a compelling, persuasive, and professional investor pitch. The output MUST strictly follow the specified tagged format for frontend rendering.

        **Tone of Voice:**
        The overall tone of the pitch MUST be: **${toneOfVoice}**.

        **Input Data:**
        - **Project Name:** ${projectName}
        - **Industry:** ${industry}
        - **Core Idea:** ${projectDescription}
        - **Problem to Solve:** ${problemStatement}
        - **Our Solution:** ${solution}
        - **Target Audience:** ${targetAudience}
        - **Competition & Our Advantage:** ${competition}
        - **Primary Objective of this Pitch:** ${pitchObjective}
        - **Funding Request:** ${fundingAmount || 'Not specified'}
        - **Growth Plan:** ${growthPlan}

        **Required Output Structure & Format Instructions:**
        Generate the pitch following this EXACT structure. You MUST use the specified ~TAGS~ for each part. Each tagged element must be on a new line. Do NOT add any extra markdown, titles, or explanations. The entire output must be only the tagged text.

        --- START OF REQUIRED FORMAT ---
        ~H~ Investor Pitch: ${projectName}

        ~S_SUB~ The Hook (Introduction)
        ~P~ [Start with a powerful opening sentence that immediately grabs attention. Introduce the project ("${projectName}") and the core vision in a compelling and confident way.]

        ~S_SUB~ The Problem
        ~P~ [Clearly and vividly describe the problem ("${problemStatement}"). Make it relatable and significant. Explain why this problem is a major pain point.]

        ~S_SUB~ Our Solution
        ~P~ [Introduce your solution ("${solution}") as the elegant and definitive answer. Explain what it is and how it works simply and attractively.]

        ~S_SUB~ Market Opportunity & Audience
        ~P~ [Define the target audience ("${targetAudience}") and explain why this is a valuable and growing customer base.]

        ~S_SUB~ The Competitive Advantage (Our Moat)
        ~P~ [Address the competition and state your unique advantages ("${competition}"). Be confident and specific about your "secret sauce".]

        ~S_SUB~ The Ask
        ~ASK~ ${theAskContent}

        ~S_SUB~ Our Vision for Growth
        ~P~ [Paint a picture of the future based on the growth plan ("${growthPlan}"). Show ambition and a clear path forward for the next 1-3 years.]

        ~S_SUB~ The Close
        ~P~ [End with a strong, memorable, and motivating closing statement. Reiterate the opportunity and invite the investor to join the journey.]
        --- END OF REQUIRED FORMAT ---

        **Final Command:**
        Generate the complete pitch text based on all instructions. The output MUST be only the tagged pitch itself, formatted as a single block of text with each tagged element on a new line.
    `;
};