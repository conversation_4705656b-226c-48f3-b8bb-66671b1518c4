import React, { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
    FiArrowRight, <PERSON>Loader, FiCheckCircle, FiCircle,
    FiThumbsUp, FiAlertTriangle
} from 'react-icons/fi';
import { updateUserSubscription, selectAuthToken } from '../../../../../../store/features/auth/authSlice';

// Import the component that knows how to parse and display the tagged text
import FormattedPitchDisplay from '../textFormate/FormattedPitchDisplay';

//==================================================================
//  1. Child Components (Self-contained within this file)
//==================================================================

/**
 * A memoized, reusable input field component.
 */
const FormInput = React.memo(({ id, label, type = "text", placeholder, value, onChange, required = true, helpText }) => (
    <div className="flex flex-col">
        <label htmlFor={id} className="block text-sm font-medium text-slate-300 mb-2">{label} {!required && <span className="text-slate-500 text-xs">(Optional)</span>}</label>
        <input type={type} id={id} name={id} value={value} onChange={onChange} placeholder={placeholder} required={required} className="w-full bg-slate-900/70 border border-slate-700 rounded-lg py-2.5 px-4 text-slate-200 placeholder-slate-500 focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500 outline-none transition-all duration-300" />
        {helpText && <p className="mt-1.5 text-xs text-slate-500">{helpText}</p>}
    </div>
));

/**
 * A memoized, reusable textarea component for longer text inputs.
 */
const FormTextarea = React.memo(({ id, label, placeholder, value, onChange, rows = 3, helpText }) => (
    <div className="flex flex-col">
        <label htmlFor={id} className="block text-sm font-medium text-slate-300 mb-2">{label}</label>
        <textarea id={id} name={id} value={value} onChange={onChange} placeholder={placeholder} required rows={rows} className="w-full bg-slate-900/70 border border-slate-700 rounded-lg py-2.5 px-4 text-slate-200 placeholder-slate-500 focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500 outline-none transition-all duration-300 resize-y" />
        {helpText && <p className="mt-1.5 text-xs text-slate-500">{helpText}</p>}
    </div>
));

/**
 * A component to display animated steps while the AI is generating the pitch.
 */
const GeneratingPitchSteps = () => {
    const steps = [
        "Analyzing Your Vision...",
        "Crafting the Core Narrative...",
        "Injecting Persuasive Language...",
        "Finalizing Your Pitch..."
    ];
    const [currentStep, setCurrentStep] = useState(0);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentStep(prev => (prev < steps.length - 1 ? prev + 1 : prev));
        }, 1800);
        return () => clearInterval(timer);
    }, [steps.length]);

    return (
        <div className="w-full bg-slate-900/50 border border-slate-700 rounded-2xl p-8 flex flex-col items-center text-center animate-fade-in">
            <h3 className="text-xl font-bold text-yellow-400 mb-6">Your Pitch is Being Generated...</h3>
            <div className="space-y-4 w-full max-w-sm">
                {steps.map((step, index) => {
                    const isCompleted = index < currentStep;
                    const isCurrent = index === currentStep;
                    return (
                        <div key={index} className="flex items-center text-left text-slate-300 transition-opacity duration-500" style={{ opacity: isCompleted || isCurrent ? 1 : 0.5 }}>
                            {isCompleted ? <FiCheckCircle className="w-6 h-6 mr-4 text-green-400 flex-shrink-0" /> : isCurrent ? <FiLoader className="w-6 h-6 mr-4 text-yellow-400 animate-spin flex-shrink-0" /> : <FiCircle className="w-6 h-6 mr-4 text-slate-500 flex-shrink-0" />}
                            <span className={isCurrent ? "font-semibold" : ""}>{step}</span>
                        </div>
                    );
                })}
            </div>
            <p className="text-slate-500 mt-8 text-sm">This may take a moment. The AI is crafting the perfect words.</p>
        </div>
    );
};


//==================================================================
//  2. The Main Form Component
//==================================================================
const InvestorPitchForm = () => {
    const dispatch = useDispatch();
    const token = useSelector(selectAuthToken);

    // State for form fields
    const [formData, setFormData] = useState({
        projectName: '', industry: '', projectDescription: '', problemStatement: '', solution: '',
        targetAudience: '', competition: '', pitchObjective: '', fundingAmount: '',
        growthPlan: '', toneOfVoice: 'Persuasive',
    });

    // State for managing API interaction (idle, loading, success, error)
    const [apiState, setApiState] = useState('idle');
    const [error, setError] = useState(null);
    const [generatedPitch, setGeneratedPitch] = useState('');

    // Memoized change handler for form inputs to prevent unnecessary re-renders
    const handleChange = useCallback((e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    }, []);

    // Function to handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        setError(null);
        setApiState('loading');

        try {
            const response = await fetch('/api/investor-pitch/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                body: JSON.stringify(formData),
            });
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            // Update user subscription in Redux if returned from backend
            if (data.subscription) {
                dispatch(updateUserSubscription(data.subscription));
            }

            setGeneratedPitch(data.generatedPitch);
            setApiState('success');
        } catch (err) {
            setError(err.message);
            setApiState('error');
            console.error('API Error:', err);
        }
    };

    // Function to reset the form and state to start over
    const handleReset = () => {
        setApiState('idle');
        setGeneratedPitch('');
        setError(null);
        // Optionally reset form data:
        // setFormData({ projectName: '', ... });
    };

    // Conditional Rendering: Show loading animation
    if (apiState === 'loading') {
        return <GeneratingPitchSteps />;
    }
    
    // Conditional Rendering: Show the formatted result
    if (apiState === 'success') {
        return <FormattedPitchDisplay pitchText={generatedPitch} onReset={handleReset} />;
    }

    // Default Render: Show the form for 'idle' or 'error' states
    return (
        <form onSubmit={handleSubmit} className="w-full space-y-6 animate-fade-in">
            <div className="grid md:grid-cols-2 gap-6">
                <FormInput id="projectName" label="Project / Company Name" placeholder="e.g., Innovatech Solutions" value={formData.projectName} onChange={handleChange} />
                <FormInput id="industry" label="Industry / Field" placeholder="e.g., SaaS, FinTech, E-commerce" value={formData.industry} onChange={handleChange} />
            </div>
            <FormTextarea id="projectDescription" label="Brief Project Description" placeholder="What is the core idea of your venture?" value={formData.projectDescription} onChange={handleChange} helpText="Summarize your venture in 1-2 powerful sentences." />
            <FormTextarea id="problemStatement" label="The Problem" placeholder="What specific pain point are you solving?" value={formData.problemStatement} onChange={handleChange} />
            <FormTextarea id="solution" label="Your Solution" placeholder="Describe your product or service and how it elegantly solves the problem." value={formData.solution} onChange={handleChange} />
            <FormTextarea id="targetAudience" label="Target Audience / Customers" placeholder="e.g., Small business owners, university students" value={formData.targetAudience} onChange={handleChange} />
            <FormTextarea id="competition" label="Competitive Advantage" placeholder="Who are your main competitors and what makes you superior?" value={formData.competition} onChange={handleChange} helpText="What is your unique selling proposition (USP)?" />
            <FormTextarea id="growthPlan" label="Future Vision / Growth Plan" placeholder="Outline your plans for the next 1-3 years." value={formData.growthPlan} onChange={handleChange} rows={4} />
            <div className="grid md:grid-cols-2 gap-6">
                <FormInput id="pitchObjective" label="Pitch Objective" placeholder="e.g., Secure Seed Funding, Find a Partner" value={formData.pitchObjective} onChange={handleChange} helpText="What is the primary goal of this pitch?" />
                <FormInput id="fundingAmount" label="Funding Amount Requested" type="text" placeholder="e.g., $50,000" value={formData.fundingAmount} onChange={handleChange} required={false} />
            </div>
            <div>
                <label htmlFor="toneOfVoice" className="block text-sm font-medium text-slate-300 mb-2">Desired Tone of Voice</label>
                <select id="toneOfVoice" name="toneOfVoice" value={formData.toneOfVoice} onChange={handleChange} className="w-full bg-slate-900/70 border border-slate-700 rounded-lg py-2.5 px-4 text-slate-200 focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500 outline-none transition-all duration-300 appearance-none bg-no-repeat" style={{ backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`, backgroundPosition: 'right 0.75rem center', backgroundSize: '1.25em 1.25em' }}>
                    <option>Persuasive</option><option>Formal</option><option>Friendly</option><option>Motivational</option><option>Professional</option>
                </select>
            </div>

            {/* Display error message if the API call fails */}
            {apiState === 'error' && error && (
                <div className="bg-red-500/10 border border-red-500/30 text-red-300 p-4 rounded-lg flex items-center text-sm">
                    <FiAlertTriangle className="w-5 h-5 mr-3 flex-shrink-0" />
                    <div><strong>Generation Failed:</strong> {error}</div>
                </div>
            )}

            <div className="pt-4 flex justify-end">
                <button type="submit" className="group flex items-center justify-center w-full sm:w-auto px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-slate-900 font-bold rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105">
                    Generate Pitch <FiArrowRight className="ml-2 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </button>
            </div>
        </form>
    );
};

export default InvestorPitchForm;