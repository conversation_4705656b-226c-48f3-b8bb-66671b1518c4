import React, { useState, useEffect } from 'react';
import { FiUser, FiLogOut, FiLogIn, FiChevronUp, FiStar, FiUpload, FiBriefcase, FiFileText, FiTarget } from 'react-icons/fi';
import IconWrapper from './IconWrapper';
import { Link } from 'react-router-dom';

// --- Plan & Limit Constants ---

const FREE_TIER_PLAN_NAME = 'Starter';
const PRO_PLAN_NAME = 'Pro';

// PDF Upload Limits
const FREE_TIER_UPLOAD_LIMIT = 5;
const PRO_TIER_UPLOAD_LIMIT = 25;

// Business Plan Limits
const FREE_TIER_BUSINESS_PLAN_LIMIT = 1;
const PRO_TIER_BUSINESS_PLAN_LIMIT = 20;

// Investor Pitch Limits
const FREE_TIER_INVESTOR_PITCH_LIMIT = 3;
const PRO_TIER_INVESTOR_PITCH_LIMIT = 25;


// --- Reusable Stat Component ---

const UsageStat = ({ icon, title, count, limit }) => {
  const percentage = limit > 0 ? (count / limit) * 100 : 0;
  const isExceeded = percentage >= 100;

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {icon}
          <span className="text-xs text-gray-300">{title}</span>
        </div>
        <span className="text-xs font-medium text-gray-200">
          {count} / {limit}
        </span>
      </div>
      <div className="w-full bg-[#2C2F33] rounded-full h-2 overflow-hidden">
        <div 
          className={`h-full transition-all duration-500 ease-out rounded-full
                    ${isExceeded 
                      ? 'bg-gradient-to-r from-red-600 to-orange-600' 
                      : percentage >= 80 
                        ? 'bg-gradient-to-r from-yellow-500 to-orange-500'
                        : 'bg-gradient-to-r from-sky-600 to-cyan-600'
                    }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
    </div>
  );
};


// --- Main Footer Component ---

const SidebarFooter = ({ isAuthenticated, currentUser, onLogoutClick, onAuthActionClick, isCollapsed }) => {
  const [isExpanded, setIsExpanded] = useState(true); 

  const userPlan = currentUser?.subscription?.planName;
  
  // Renders the Uploads stat
  const renderUploadUsage = () => {
    if (!currentUser?.subscription) return null;

    let uploadCount, uploadLimit;
    if (userPlan === FREE_TIER_PLAN_NAME) {
      uploadCount = currentUser.subscription.freeTierUploadCount ?? 0;
      uploadLimit = FREE_TIER_UPLOAD_LIMIT;
    } else if (userPlan === PRO_PLAN_NAME) {
      uploadCount = currentUser.subscription.proTierUploadCount ?? 0;
      uploadLimit = PRO_TIER_UPLOAD_LIMIT;
    } else {
        return null; // Don't show if plan is unknown or not subscribed
    }

    const shouldShowUpgrade = userPlan === FREE_TIER_PLAN_NAME && uploadCount >= uploadLimit;

    return (
      <div className="pt-1">
        <UsageStat icon={<FiUpload className="w-3 h-3 text-gray-400" />} title="Uploads" count={uploadCount} limit={uploadLimit} />
        {shouldShowUpgrade && (
          <Link to="/pricing" className="inline-flex items-center space-x-1 text-xs font-medium text-yellow-400 hover:text-yellow-300 transition-colors duration-200 hover:underline pt-2">
            <FiStar className="w-3 h-3" />
            <span>Upgrade Plan</span>
          </Link>
        )}
      </div>
    );
  };

  // Renders the Business Plans stat
  const renderBusinessPlanUsage = () => {
    if (!currentUser?.subscription) return null;

    let planCount, planLimit;
    if (userPlan === FREE_TIER_PLAN_NAME) {
      planCount = currentUser.subscription.freeTierBusinessPlanCount ?? 0;
      planLimit = FREE_TIER_BUSINESS_PLAN_LIMIT;
    } else if (userPlan === PRO_PLAN_NAME) {
      planCount = currentUser.subscription.proTierBusinessPlanCount ?? 0;
      planLimit = PRO_TIER_BUSINESS_PLAN_LIMIT;
    } else {
        return null; // Don't show if plan is unknown or not subscribed
    }

    const shouldShowUpgrade = userPlan === FREE_TIER_PLAN_NAME && planCount >= planLimit;

    return (
      <div className="pt-1">
        <UsageStat icon={<FiFileText className="w-3 h-3 text-gray-400" />} title="Plans" count={planCount} limit={planLimit} />
        {shouldShowUpgrade && (
          <Link to="/pricing" className="inline-flex items-center space-x-1 text-xs font-medium text-yellow-400 hover:text-yellow-300 transition-colors duration-200 hover:underline pt-2">
            <FiStar className="w-3 h-3" />
            <span>Upgrade Plan</span>
          </Link>
        )}
      </div>
    );
  };

  // Renders the Investor Pitches stat
  const renderInvestorPitchUsage = () => {
    if (!currentUser?.subscription) return null;

    let pitchCount, pitchLimit;
    if (userPlan === FREE_TIER_PLAN_NAME) {
      pitchCount = currentUser.subscription.freeTierInvestorPitchCount ?? 0;
      pitchLimit = FREE_TIER_INVESTOR_PITCH_LIMIT;
    } else if (userPlan === PRO_PLAN_NAME) {
      pitchCount = currentUser.subscription.proTierInvestorPitchCount ?? 0;
      pitchLimit = PRO_TIER_INVESTOR_PITCH_LIMIT;
    } else {
        return null; // Don't show if plan is unknown or not subscribed
    }

    const shouldShowUpgrade = userPlan === FREE_TIER_PLAN_NAME && pitchCount >= pitchLimit;

    return (
      <div className="pt-1">
        <UsageStat icon={<FiTarget className="w-3 h-3 text-gray-400" />} title="Pitches" count={pitchCount} limit={pitchLimit} />
        {shouldShowUpgrade && (
          <Link to="/pricing" className="inline-flex items-center space-x-1 text-xs font-medium text-yellow-400 hover:text-yellow-300 transition-colors duration-200 hover:underline pt-2">
            <FiStar className="w-3 h-3" />
            <span>Upgrade Plan</span>
          </Link>
        )}
      </div>
    );
  };


  // --- Render Logic ---

  if (isCollapsed) {
    // Return unchanged collapsed view
    return (
      <div className="p-3 mt-auto border-t border-[#2C2F33]/30 shrink-0">
        {isAuthenticated && currentUser ? (
          <div className="flex flex-col items-center space-y-3">
            <div className="relative group/avatar">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-sky-600 to-cyan-700 flex items-center justify-center text-white font-semibold text-sm shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer hover:scale-105">
                {currentUser.name ? currentUser.name.charAt(0).toUpperCase() : currentUser.email ? currentUser.email.charAt(0).toUpperCase() : 'U'}
              </div>
              <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full bg-green-500 border-2 border-[#151719] shadow-sm" />
              <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 bg-[#1A1C1E] text-white px-3 py-2 rounded-lg text-sm opacity-0 invisible group-hover/avatar:opacity-100 group-hover/avatar:visible transition-all duration-200 delay-300 z-50 border border-[#2C2F33] shadow-xl min-w-max">
                <div className="font-medium">{currentUser.name || currentUser.email}</div>
                <div className="text-xs text-gray-400 mt-1">Plan: {userPlan || 'N/A'}</div>
                <div className="absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-[#1A1C1E]" />
              </div>
            </div>
            <button onClick={onLogoutClick} className="w-10 h-10 rounded-lg bg-red-700/80 hover:bg-red-600/90 text-white transition-all duration-200 flex items-center justify-center hover:scale-105 shadow-lg hover:shadow-xl group/logout" title="Logout">
              <FiLogOut className="w-4 h-4 group-hover/logout:scale-110 transition-transform duration-200" />
            </button>
          </div>
        ) : (
          <button onClick={onAuthActionClick} className="w-10 h-10 rounded-lg bg-sky-700 hover:bg-sky-600 text-white transition-all duration-200 flex items-center justify-center hover:scale-105 shadow-lg hover:shadow-xl" title="Login / Register">
            <FiLogIn className="w-4 h-4" />
          </button>
        )}
      </div>
    );
  }

  // Return expanded view
  return (
    <div className="p-4 mt-auto border-t border-[#2C2F33]/30 shrink-0">
      {isAuthenticated && currentUser ? (
        <div className="space-y-4">
          <div className="relative cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
            <div className="flex items-center p-3 rounded-xl bg-[#1A1C1E]/70 hover:bg-[#232527]/70 transition-all duration-200 group/user border border-[#2C2F33]/50">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-sky-600 to-cyan-700 flex items-center justify-center text-white font-semibold text-sm shadow-lg group-hover/user:shadow-xl transition-all duration-200 group-hover/user:scale-105 mr-3">
                {currentUser.name ? currentUser.name.charAt(0).toUpperCase() : currentUser.email ? currentUser.email.charAt(0).toUpperCase() : 'U'}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-100 truncate">{currentUser.name || currentUser.email || "User"}</span>
                  {userPlan === PRO_PLAN_NAME && (<FiStar className="w-4 h-4 text-yellow-400 flex-shrink-0" />)}
                </div>
                {currentUser.name && currentUser.email && (<span className="text-xs text-gray-400 truncate block">{currentUser.email}</span>)}
              </div>
              <FiChevronUp className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} />
            </div>
            <div className="absolute top-2 right-2 w-3 h-3 rounded-full bg-green-500 border-2 border-[#1A1C1E] shadow-sm" />
          </div>

          {isExpanded && currentUser.subscription && (
            <div className="space-y-3 px-1 animate-in slide-in-from-top-2 duration-200">
              <div className="flex items-center justify-between p-3 rounded-lg bg-[#151719]/50 border border-[#232527]/40">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${userPlan === PRO_PLAN_NAME ? 'bg-yellow-500' : 'bg-sky-500'}`} />
                  <span className="text-xs text-gray-300">Plan</span>
                </div>
                <span className={`text-xs font-semibold ${userPlan === PRO_PLAN_NAME ? 'text-yellow-400' : 'text-sky-400'}`}>{userPlan || 'N/A'}</span>
              </div>
              
              {renderUploadUsage()}
              {renderBusinessPlanUsage()}
              {renderInvestorPitchUsage()}

            </div>
          )}

          <button onClick={onLogoutClick} className="w-full flex items-center justify-center px-4 py-2.5 rounded-xl bg-gradient-to-r from-red-700 to-red-600 hover:from-red-600 hover:to-red-500 text-white text-sm font-medium transition-all duration-200 hover:scale-[1.02] hover:shadow-lg hover:shadow-red-950/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-[#1A1C1E] group/logout">
            <IconWrapper><FiLogOut className="w-4 h-4 mr-2 group-hover/logout:scale-110 transition-transform duration-200" /></IconWrapper>
            Logout
          </button>
        </div>
      ) : (
        <button onClick={onAuthActionClick} className="w-full flex items-center justify-center px-4 py-2.5 rounded-xl bg-gradient-to-r from-sky-700 to-cyan-700 hover:from-sky-600 hover:to-cyan-600 text-white text-sm font-medium transition-all duration-200 hover:scale-[1.02] hover:shadow-lg hover:shadow-sky-950/30 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 focus:ring-offset-[#1A1C1E] group/login">
          <IconWrapper><FiLogIn className="w-4 h-4 mr-2 group-hover/login:scale-110 transition-transform duration-200" /></IconWrapper>
          Login / Register
        </button>
      )}
    </div>
  );
};

export default SidebarFooter;