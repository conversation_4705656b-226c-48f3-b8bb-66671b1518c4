// node_gemini_summarizer/controllers/internal/mindmapInternalController.js
import crypto from 'crypto';
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from "@google/generative-ai";

// --- Gemini Configuration ---
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_MODEL_NAME = process.env.GEMINI_MINDMAP_MODEL_NAME || "gemini-1.5-flash-latest";

let genAIInstance;
let modelInstance;

function initializeGemini() {
    if (!GEMINI_API_KEY) {
        console.error("[Node Gemini Mindmap Controller FATAL ERROR]: GEMINI_API_KEY is not defined. Mindmap generation via AI will not work.");
        return null;
    }
    try {
        genAIInstance = new GoogleGenerativeAI(GEMINI_API_KEY);
        modelInstance = genAIInstance.getGenerativeModel({ model: GEMINI_MODEL_NAME });
        console.log(`[Node Gemini Mindmap Controller] Initialized Gemini with model: ${GEMINI_MODEL_NAME}`);
        return modelInstance;
    } catch (error) {
        console.error("[Node Gemini Mindmap Controller] Failed to initialize Gemini Service:", error);
        modelInstance = null;
        return null;
    }
}

// Initialize on load
if (!modelInstance) {
    modelInstance = initializeGemini();
}

const getGeminiModel = () => {
    if (!modelInstance) {
        console.warn("[Node Gemini Mindmap Controller] Attempting to re-initialize Gemini Model...");
        return initializeGemini();
    }
    return modelInstance;
};

// Basic cache (in-memory, for demonstration. Use Redis in production)
const summaryCache = new Map();

// --- Helper function to parse Gemini's JSON output ---
function parseGeminiJsonResponse(geminiResponseText, originalFileName) {
    try {
        // Gemini might sometimes wrap the JSON in ```json ... ``` or have leading/trailing whitespace.
        let cleanedJsonText = geminiResponseText.trim();
        if (cleanedJsonText.startsWith("```json")) {
            cleanedJsonText = cleanedJsonText.substring(7); // Remove ```json
            if (cleanedJsonText.endsWith("```")) {
                cleanedJsonText = cleanedJsonText.slice(0, -3); // Remove ```
            }
        }
        cleanedJsonText = cleanedJsonText.trim();

        const jsonData = JSON.parse(cleanedJsonText);
        // Basic validation: ensure it has a "name" property
        if (typeof jsonData.name !== 'string') {
            console.warn("[Node Gemini Mindmap Controller] Gemini JSON response missing 'name' root property. Raw:", geminiResponseText);
            return { name: originalFileName || "Mind Map", error: "AI response was not in the expected mind map format (missing root name)." };
        }
        return jsonData;
    } catch (parseError) {
        console.error("[Node Gemini Mindmap Controller] Failed to parse JSON response from Gemini:", parseError);
        console.error("[Node Gemini Mindmap Controller] Raw Gemini response causing parse error:", geminiResponseText);
        return {
            name: originalFileName || "Mind Map",
            error: "Failed to parse mind map structure from AI. The response was not valid JSON.",
            details: parseError.message,
            rawResponse: process.env.NODE_ENV !== 'production' ? geminiResponseText.substring(0, 500) + "..." : undefined
        };
    }
}


export const generateMindmapStructure = async (req, res) => {
    const { 
        extractedPdfText, 
        originalFileName = "Untitled Document", 
        language = 'English' // Default to English if not provided
    } = req.body;
    const workerPid = process.pid;

    if (!extractedPdfText) {
        console.warn(`[Node Worker ${workerPid}] Received request to generate mindmap without extracted PDF text.`);
        return res.status(400).json({ error: 'Extracted PDF text is required.' });
    }

    const model = getGeminiModel();
    if (!model) {
        console.error(`[Node Worker ${workerPid}] Gemini model not initialized. Cannot generate mindmap.`);
        return res.status(500).json({ error: 'AI Model for mindmap generation is not available.' });
    }

    const textHash = crypto.createHash('md5').update(extractedPdfText.substring(0, 20000)).digest('hex');
    const cacheKey = `${textHash}-${language}`; // Make the cache key language-specific

    if (summaryCache.has(cacheKey) && process.env.NODE_ENV !== 'development') {
        console.log(`[Node Worker ${workerPid}] Returning cached mindmap structure for cache key: ${cacheKey}`);
        return res.status(200).json(summaryCache.get(cacheKey));
    }

    console.log(`[Node Worker ${workerPid}] Generating new mindmap structure for document: "${originalFileName}" in ${language} using Gemini...`);
    console.log(`[Node Worker ${workerPid}] Extracted text length: ${extractedPdfText.length} characters.`);

    const prompt = `
            You are an AI assistant specialized in creating structured mind maps from text.
            Analyze the following text, which was extracted from a PDF document titled "${originalFileName}".
            Your task is to generate a hierarchical mind map structure representing the key topics, sub-topics, and main ideas from the text.
            
            The output MUST be a single, valid JSON object. Do not include any text, comments, or explanations before or after the JSON object itself.
            
            *** CRITICAL INSTRUCTION: The entire response, including all "name" and "value" fields in the JSON, MUST be in the following language: ${language}. ***
            
            The JSON structure should be like this:
            {
              "name": "Central Topic or Document Title (in ${language})",
              "children": [
                {
                  "name": "Main Topic 1 (in ${language})",
                  "children": [
                    { "name": "Sub-topic 1.1 (in ${language})", "value": "Brief detail/summary for this node in ${language} (optional)" },
                    { "name": "Sub-topic 1.2 (in ${language})" }
                  ]
                },
                { "name": "Main Topic 2 (in ${language})"},
                {
                  "name": "Main Topic 3 (in ${language})",
                  "children": [ { "name": "Sub-point 3.1 (in ${language})" } ]
                }
              ]
            }
            The root "name" of the mind map should accurately reflect the document's main subject or title.
            When determining main topics, prioritize distinct, high-level themes. If several related concepts can be logically grouped under a common parent topic, prefer creating such a parent topic rather than having many closely related main topics directly under the root. This structural choice helps in creating a mind map that allows for clearer visual separation between major branches when it is rendered.
            Node names ("name" fields) should be concise and descriptive.
            If the provided text is empty or contains no meaningful content to construct a mind map, you MUST return this specific JSON object (respecting the output language rule):
            { "name": "${originalFileName}", "error": "No meaningful content found in the PDF to generate a mind map." }

            Extracted PDF Text:
            ---
            ${extractedPdfText.substring(0, 180000)}
            ---
            Now, generate ONLY the valid JSON mind map structure based on the text, strictly in the ${language} language.
        `;

    try {
        const generationConfig = {
            temperature: 0.6,
            topK: 1,
            topP: 1,
            maxOutputTokens: 8192,
            responseMimeType: "application/json",
        };

        const safetySettings = [
            { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
            { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
            { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
            { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
        ];

        console.log(`[Node Worker ${workerPid}] Sending prompt to Gemini for language: ${language}. First 100 chars: "${extractedPdfText.substring(0,100)}..."`);

        const result = await model.generateContent({
            contents: [{ role: "user", parts: [{text: prompt}] }],
            generationConfig,
            safetySettings,
        });

        const responseText = result.response.text();

        if (!responseText) {
            console.warn(`[Node Worker ${workerPid}] Gemini returned an empty response text. Feedback:`, result.response.promptFeedback);
            if (result.response.promptFeedback && result.response.promptFeedback.blockReason) {
                const blockReasonMessage = `Content generation blocked by Gemini. Reason: ${result.response.promptFeedback.blockReason}.`;
                console.error(`[Node Worker ${workerPid}] ${blockReasonMessage}`);
                return res.status(500).json({ name: originalFileName, error: blockReasonMessage });
            }
            return res.status(500).json({ name: originalFileName, error: "AI returned an empty response." });
        }

        console.log(`[Node Worker ${workerPid}] Received response from Gemini. Length: ${responseText.length}. First 100 chars: "${responseText.substring(0,100)}..."`);

        const mindMapData = parseGeminiJsonResponse(responseText, originalFileName);

        if (mindMapData.error) {
            console.warn(`[Node Worker ${workerPid}] Mindmap data from Gemini contained an error or was unparsable: ${mindMapData.error}`);
            return res.status(mindMapData.rawResponse ? 500 : 200).json(mindMapData);
        }

        summaryCache.set(cacheKey, mindMapData);
        // Evict oldest entry if cache grows too large
        if (summaryCache.size > 500) {
            const firstKey = summaryCache.keys().next().value;
            summaryCache.delete(firstKey);
        }

        console.log(`[Node Worker ${workerPid}] Successfully generated and parsed mindmap structure from Gemini. Root: ${mindMapData.name}`);
        res.status(200).json(mindMapData);

    } catch (error) {
        console.error(`[Node Worker ${workerPid}] Error generating mindmap structure with Gemini:`, error);
        if (error.message && (error.message.includes("API key not valid") || error.message.includes("PERMISSION_DENIED"))) {
             return res.status(401).json({ error: 'AI service authentication failed.', details: error.message });
        }
        if (error.message && error.message.includes("Rate limit exceeded")) {
             return res.status(429).json({ error: 'AI service rate limit exceeded.', details: error.message });
        }
        res.status(500).json({
            error: 'Failed to generate mind map structure using AI.',
            details: error.message
        });
    }
};